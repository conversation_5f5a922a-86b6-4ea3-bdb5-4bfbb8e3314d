import dayjs, { Dayjs } from 'dayjs';
import isoWeek from 'dayjs/plugin/isoWeek';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { FileDisplay } from '../file/fileTypes';
import { GroupDisplay, GroupListRead } from '../group/groupTypes';
import { MeasureRead } from './measure/measureTypes';
import { UserDisplay } from '../user/userTypes';
import { WorkMethodRead, WorkMethodType } from './work-method/workMethodTypes';
import { LocationDisplay, LocationFilterMode } from '../location/locationTypes';
import { DocumentDisplay } from '../documents/documentTypes';
import { LototoDisplay } from '../lototo/lototoTypes';
import { EnumMetaMap } from '../../types';
import { themeToColor } from '../../theme';

// Configure dayjs for week and timezone calculations
dayjs.extend(isoWeek);
dayjs.extend(timezone);
dayjs.extend(utc);

export interface WorkPermitParams {
  ancestorGroupId?: number;
  groupId?: number;
  locationOwner?: number;
  locationOwnerAnscestor?: number;
  locationId?: number;
  ancestorLocationId?: number;
  status?: WorkPermitStatus;
  statusNot?: WorkPermitStatus;
  riskCategory?: RiskCategory;
  workMethodIds?: number[];
  createdBy?: number;
  lototoPlan?: number;
  pageSize?: number;
  pageNumber?: number;
  candidateGroups?: WorkPermitCandidatGroup[];
  excludeUser?: number;
  filter?: string;
  search?: string;
  sort?: WorkPermitSort[];
}

export interface WorkPermitPDFParams {
  id: number;
  timeZone: string;
  copy?: boolean;
}

export interface WorkPermitViewState {
  listView: 'mine' | 'all';
  viewMode?: 'table' | 'map';
  group?: GroupListRead;
  status?: WorkPermitStatus;
  riskCategory?: RiskCategory;
  workMethods?: WorkMethodRead[];
  createdBy?: number;
  candidateGroups?: WorkPermitCandidatGroup[];
  search?: string;
  location?: LocationDisplay;
  locationFilterMode?: LocationFilterMode;
  columns?: WorkPermitColumnSetting[];
  sort?: WorkPermitSort[];
}

export enum WorkPermitCandidatGroup {
  WORKPERMIT_PROCESS_FEEDBACK = 'WORKPERMIT_PROCESS_FEEDBACK',
  WORKPERMIT_REVIEW = 'WORKPERMIT_REVIEW',
  WORKPERMIT_APPROVE_HIGH_RISK = 'WORKPERMIT_APPROVE_HIGH_RISK',
  WORKPERMIT_APPROVE_SPECIAL_SITUATION = 'WORKPERMIT_APPROVE_SPECIAL_SITUATION',
  WORKPERMIT_PREPARE = 'WORKPERMIT_PREPARE',
  WORKPERMIT_ISSUE = 'WORKPERMIT_ISSUE',
  WORKPERMIT_REVOKE = 'WORKPERMIT_REVOKE',
}

export const WorkPermitCandidateGroupDisplayMap: Record<WorkPermitCandidatGroup, string> = {
  WORKPERMIT_PROCESS_FEEDBACK: 'Process feedback',
  WORKPERMIT_REVIEW: 'Review',
  WORKPERMIT_APPROVE_HIGH_RISK: 'Approve high risk',
  WORKPERMIT_APPROVE_SPECIAL_SITUATION: 'Approve special situation',
  WORKPERMIT_PREPARE: 'Prepare',
  WORKPERMIT_ISSUE: 'Issue',
  WORKPERMIT_REVOKE: 'Revoke',
};

export enum WorkPermitStatus {
  REQUESTED = 'REQUESTED',
  APPROVED = 'APPROVED',
  PREPARED = 'PREPARED',
  ISSUED = 'ISSUED',
  CLOSED = 'CLOSED',
  CANCELED = 'CANCELED',
}

export const WorkPermitStatusMeta: EnumMetaMap<WorkPermitStatus> = {
  [WorkPermitStatus.REQUESTED]: { label: 'Requested', color: themeToColor('primary.main') },
  [WorkPermitStatus.APPROVED]: { label: 'Approved', color: themeToColor('secondary.main') },
  [WorkPermitStatus.PREPARED]: { label: 'Prepared', color: themeToColor('success.main') },
  [WorkPermitStatus.ISSUED]: { label: 'Issued', color: themeToColor('warning.light') },
  [WorkPermitStatus.CLOSED]: { label: 'Closed', color: themeToColor('warning.main') },
  [WorkPermitStatus.CANCELED]: { label: 'Canceled', color: themeToColor('error.main') },
};

export const WorkPermitStatusDisplayMap = Object.fromEntries(
  Object.entries(WorkPermitStatusMeta).map(([key, meta]) => [key, meta?.label ?? ''])
) as Record<WorkPermitStatus, string>;

export enum RiskCategory {
  HIGH = 'HIGH',
  LOW = 'LOW',
}

export const RiskCategoryMeta: EnumMetaMap<RiskCategory> = {
  [RiskCategory.HIGH]: { label: 'High', color: themeToColor('error.main') },
  [RiskCategory.LOW]: { label: 'Low', color: themeToColor('warning.light') },
};

export const RiskCategoryDisplayMap = Object.fromEntries(
  Object.entries(RiskCategoryMeta).map(([key, meta]) => [key, meta?.label ?? ''])
) as Record<RiskCategory, string>;

export const RiskCategoryColorMap = Object.fromEntries(
  Object.entries(RiskCategoryMeta).map(([key, meta]) => [key, meta?.color ?? ''])
) as Record<RiskCategory, string>;

export enum AtexZone {
  ATEX_ZONE_0 = 'ATEX_ZONE_0',
  ATEX_ZONE_1 = 'ATEX_ZONE_1',
  ATEX_ZONE_2 = 'ATEX_ZONE_2',
  NONE = 'NONE',
}

export const AtexZoneMeta: EnumMetaMap<AtexZone> = {
  [AtexZone.ATEX_ZONE_0]: { label: 'ATEX zone 0', color: themeToColor('error.main') },
  [AtexZone.ATEX_ZONE_1]: { label: 'ATEX zone 1', color: themeToColor('secondary.main') },
  [AtexZone.ATEX_ZONE_2]: { label: 'ATEX zone 2', color: themeToColor('success.main') },
  [AtexZone.NONE]: { label: 'None', color: themeToColor('warning.main') },
};

export const AtexZoneDisplayMap = Object.fromEntries(
  Object.entries(AtexZoneMeta).map(([key, meta]) => [key, meta?.label ?? ''])
) as Record<AtexZone, string>;

export interface WorkPermitRead {
  id: number;
  name: string;
  sid: number;
  description: string;
  holder: string;
  additionalRequirements: string;
  group: GroupDisplay;
  status: WorkPermitStatus;
  locationOwner: GroupDisplay;
  riskCategory: RiskCategory;
  tra?: TraDisplay;
  processInstanceId: string;
  location: LocationDisplay;
  atexZone: AtexZone;
  workMethods: WorkPermitWorkMethodRead[];
  measures: WorkPermitMeasureRead[];
  equipment: string;
  tools: string;
  creationDate: number;
  createdBy: UserDisplay;
  modifiedDate: number;
  modifiedBy: UserDisplay;
  startTime: number;
  endTime: number;
  workerCount: number;
  actualWorkerCount: number;
  locked: boolean;
  lototoPlans: LototoDisplay[];
  documents: DocumentDisplay[];
  files: FileDisplay[];
}

export interface WorkPermitCreate {
  name: string;
  description: string;
  groupId: number;
  riskCategory: RiskCategory;
  tra: number | null;
  location: number;
  atexZone: AtexZone;
  workMethods: WorkPermitWorkMethodCreate[];
  measures: WorkPermitMeasureCreate[];
  equipment: string;
  tools: string;
  startTime: number;
  endTime: number;
  workerCount: number;
  lototoPlans: number[];
  documents: number[];
  files: number[];
}

export interface WorkPermitUpdate {
  id: number;
  name: string;
  description: string;
  riskCategory: RiskCategory;
  tra: number | null;
  location: number;
  atexZone: AtexZone;
  workMethods: WorkPermitWorkMethodUpdate[];
  measures: WorkPermitMeasureUpdate[];
  equipment: string;
  tools: string;
  startTime: number;
  endTime: number;
  workerCount: number;
  lototoPlans: number[];
  documents: number[];
  files: number[];
}

export interface WorkPermitMeasureCreate {
  measure: number;
  required: boolean;
  note: string;
}

export interface WorkPermitMeasureUpdate {
  id: number;
  measure: number;
  required: boolean;
  note: string;
}

export interface WorkPermitMeasureRead {
  id?: number;
  measure: MeasureRead;
  required: boolean;
  note: string;
}

export interface WorkPermitMeasureForm {
  id?: number;
  measure: number;
  name: string;
  noteType: string;
  note: string;
  required: boolean;
  mustNotBeEmpty: boolean;
  focus?: boolean;
}

export interface WorkPermitWorkMethodCreate {
  workMethod: number;
  required: boolean;
}

export interface WorkPermitWorkMethodUpdate {
  id: number;
  workMethod: number;
  required: boolean;
}

export interface WorkPermitWorkMethodRead {
  id: number;
  workMethod: WorkMethodRead;
  required: boolean;
}

export interface WorkPermitWorkMethodForm {
  id?: number;
  workMethod: number;
  name: string;
  type: WorkMethodType;
  required: boolean;
}

export type MeasureTypeForm = 'ppeHolder' | 'measuresHolder' | 'measuresIssuer' | 'fieldsIssuer';

export interface WorkPermitFormInput {
  group: GroupDisplay | null;
  name: string;
  description: string;
  location: LocationDisplay | null;
  atexZone: AtexZone | null;
  workMethods: WorkPermitWorkMethodForm[];
  tools: string;
  equipment: string;
  riskCategory: RiskCategory | null;
  tra: TraDisplay | null;
  startDate: Dayjs | null;
  endDate: Dayjs | null;
  startTime: Dayjs | null;
  endTime: Dayjs | null;
  workerCount: number | null;
  measuresHolder: WorkPermitMeasureForm[];
  ppeHolder: WorkPermitMeasureForm[];
  measuresIssuer: WorkPermitMeasureForm[];
  fieldsIssuer: WorkPermitMeasureForm[];
  lototoPlans: LototoDisplay[];
  documents: DocumentDisplay[];
  files: FileDisplay[];
}

export interface WorkPermitCopy {
  name: string;
  description: string;
  location: LocationDisplay | null;
  atexZone: AtexZone | null;
  workMethods: WorkPermitWorkMethodForm[];
  tools: string;
  equipment: string;
  riskCategory: RiskCategory | null;
  startDate: number | null;
  endDate: number | null;
  startTime: number | null;
  endTime: number | null;
  workerCount: number | null;
  measuresHolder: WorkPermitMeasureForm[];
  ppeHolder: WorkPermitMeasureForm[];
  measuresIssuer: WorkPermitMeasureForm[];
  fieldsIssuer: WorkPermitMeasureForm[];
  documents: DocumentDisplay[];
  files: FileDisplay[];
}

export interface WorkPermitDisplay {
  id: number;
  sid: number;
  name: string;
  locked: boolean;
}

export interface WorkPermitChange {
  by: UserDisplay;
  at: number;
  type: WorkPermitChangeType;
  oldEntity: WorkPermitRead;
  newEntity: WorkPermitRead;
}

export enum WorkPermitChangeType {
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
}

export const WorkPermitChangeTypeDisplayMap: Record<WorkPermitChangeType, string> = {
  INSERT: 'Work permit requested',
  UPDATE: 'Work permit updated',
  DELETE: 'Work permit deleted',
};

export interface TraDisplay {
  id: number;
  sid: number;
  name: string;
}

export interface WorkPermitState {
  workPermitCopy?: Partial<WorkPermitCopy>;
  workPermitViewState: WorkPermitViewState;
}

export enum WorkPermitColumn {
  SID = 'sid',
  NAME = 'name',
  GROUP = 'group',
  LOCATION = 'location',
  RISK_CATEGORY = 'riskCategory',
  STATUS = 'status',
  DATE = 'date',
}

export const WorkPermitColumnDisplayMap: Record<WorkPermitColumn, string> = {
  [WorkPermitColumn.SID]: 'ID',
  [WorkPermitColumn.NAME]: 'Title',
  [WorkPermitColumn.GROUP]: 'Created by',
  [WorkPermitColumn.LOCATION]: 'Location',
  [WorkPermitColumn.RISK_CATEGORY]: 'Risk',
  [WorkPermitColumn.STATUS]: 'Status',
  [WorkPermitColumn.DATE]: 'Date',
};

export interface WorkPermitColumnSetting {
  column: WorkPermitColumn;
  hidden: boolean;
  width: number;
}

export const WorkPermitColumnDefaults: WorkPermitColumnSetting[] = [
  { column: WorkPermitColumn.SID, width: 75, hidden: false },
  { column: WorkPermitColumn.NAME, width: 675, hidden: false },
  { column: WorkPermitColumn.GROUP, width: 150, hidden: false },
  { column: WorkPermitColumn.LOCATION, width: 150, hidden: false },
  { column: WorkPermitColumn.RISK_CATEGORY, width: 100, hidden: false },
  { column: WorkPermitColumn.STATUS, width: 125, hidden: false },
  { column: WorkPermitColumn.DATE, width: 300, hidden: false },
];

export enum WorkPermitSortField {
  SID = 'sid',
  NAME = 'name',
  RISK_CATEGORY = 'risk_category',
  STATUS = 'status',
  DATE = 'start_time',
  CREATION_DATE = 'creation_date',
  MODIFIED_DATE = 'modified_date',
}

export const WorkPermitFieldSortMap: Partial<Record<keyof WorkPermitRead, WorkPermitSortField>> = {
  sid: WorkPermitSortField.SID,
  name: WorkPermitSortField.NAME,
  riskCategory: WorkPermitSortField.RISK_CATEGORY,
  status: WorkPermitSortField.STATUS,
  startTime: WorkPermitSortField.DATE,
  creationDate: WorkPermitSortField.CREATION_DATE,
  modifiedDate: WorkPermitSortField.MODIFIED_DATE,
};

export interface WorkPermitSort {
  field: WorkPermitSortField;
  direction: 'asc' | 'desc';
}

export enum WorkPermitGroupBy {
  GROUP = 'WORKPERMITS_GROUP',
  REQUESTED_BY = 'WORKPERMITS_REQUESTED_BY',
  WORK_METHOD = 'WORKPERMITS_WORK_METHOD',
  MEASURE = 'WORKPERMITS_MEASURE',
  RISK = 'WORKPERMITS_RISK',
  ZONE = 'WORKPERMITS_ZONE',
  STATUS = 'WORKPERMITS_STATUS',
  LOCATION = 'WORKPERMITS_LOCATION',
  ROOT_LOCATION = 'WORKPERMITS_ROOT_LOCATION',
  CREATION_DATE_WEEK = 'WORKPERMITS_CREATION_DATE_WEEK',
}
export const WorkPermitGroupByDisplayMap: Record<WorkPermitGroupBy, string> = {
  [WorkPermitGroupBy.RISK]: 'Risk',
  [WorkPermitGroupBy.ZONE]: 'Zone',
  [WorkPermitGroupBy.STATUS]: 'Status',
  [WorkPermitGroupBy.WORK_METHOD]: 'Work conditions',
  [WorkPermitGroupBy.MEASURE]: 'Measures',
  [WorkPermitGroupBy.GROUP]: 'Group',
  [WorkPermitGroupBy.REQUESTED_BY]: 'User',
  [WorkPermitGroupBy.LOCATION]: 'Location',
  [WorkPermitGroupBy.ROOT_LOCATION]: 'Root Location',
  [WorkPermitGroupBy.CREATION_DATE_WEEK]: 'Work permit week',
};
export interface WorkPermitGroupByFieldType {
  [WorkPermitGroupBy.RISK]: RiskCategory;
  [WorkPermitGroupBy.ZONE]: AtexZone;
  [WorkPermitGroupBy.STATUS]: WorkPermitStatus;
  [WorkPermitGroupBy.CREATION_DATE_WEEK]: string; // ISO date strings
}

/**
 * Transforms an ISO date string into a formatted "Week X, YYYY" string
 * using the user's current timezone for the calculation.
 *
 * @param isoDateString - ISO date string (e.g., "2025-02-17T00:00:00.000+00:00")
 * @returns Formatted string like "Week 8, 2025"
 */
export function transformCreationDateWeek(isoDateString: string): string {
  try {
    // Handle empty or null strings
    if (!isoDateString || isoDateString.trim() === '') {
      return 'Invalid Date';
    }

    // Get user's timezone
    const userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    // Parse the ISO date string and convert to user's timezone
    const date = dayjs(isoDateString).tz(userTimeZone);

    // Check if the date is valid
    if (!date.isValid()) {
      return 'Invalid Date';
    }

    // Get ISO week number and year (use isoWeekYear for correct year calculation)
    const weekNumber = date.isoWeek();
    const year = date.isoWeekYear();

    return `Week ${weekNumber}, ${year}`;
  } catch {
    // Fallback for invalid dates
    return 'Invalid Date';
  }
}

/**
 * Dynamic meta map for CREATION_DATE_WEEK that transforms ISO date strings to week labels
 */
const CreationDateWeekMeta: EnumMetaMap<string> = new Proxy({} as EnumMetaMap<string>, {
  get(_target, prop: string) {
    if (typeof prop === 'string') {
      return {
        label: transformCreationDateWeek(prop),
        color: themeToColor('primary.main'), // Default color for week entries
      };
    }
    return undefined;
  },
  has(_target, prop) {
    // Always return true for string properties (ISO date strings)
    return typeof prop === 'string';
  },
  ownKeys() {
    // Return empty array since this is a dynamic proxy
    return [];
  },
  getOwnPropertyDescriptor(_target, prop) {
    if (typeof prop === 'string') {
      return {
        enumerable: true,
        configurable: true,
        value: {
          label: transformCreationDateWeek(prop),
          color: themeToColor('primary.main'),
        },
      };
    }
    return undefined;
  },
});

export const WorkPermitGroupByFieldMetaMap: {
  [K in keyof WorkPermitGroupByFieldType]: EnumMetaMap<WorkPermitGroupByFieldType[K]>;
} = {
  [WorkPermitGroupBy.RISK]: RiskCategoryMeta,
  [WorkPermitGroupBy.ZONE]: AtexZoneMeta,
  [WorkPermitGroupBy.STATUS]: WorkPermitStatusMeta,
  [WorkPermitGroupBy.CREATION_DATE_WEEK]: CreationDateWeekMeta,
};

export const WorkPermitGroupByFieldSortFunctionMap = {
  [WorkPermitGroupBy.CREATION_DATE_WEEK]: (a: string, b: string) => {
    const dateA = dayjs(a);
    const dateB = dayjs(b);
    return dateA.isBefore(dateB) ? -1 : 1;
  },
};
