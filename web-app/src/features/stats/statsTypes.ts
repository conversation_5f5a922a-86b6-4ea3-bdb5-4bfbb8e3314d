import { EnumMetaMap } from '../../types';
import { mapEnumValues } from '../../utils';
import { ExternalAuditGroupBy, ExternalAuditGroupByDisplayMap } from '../auditexternal/externalAuditTypes';
import { InternalAuditGroupBy, InternalAuditGroupByDisplayMap } from '../auditinternal/internalAuditTypes';
import { ChangeGroupBy, ChangeGroupByDisplayMap } from '../change/changeTypes';
import { ChecklistAnswerGroupBy, ChecklistAnswerGroupByDisplayMap } from '../checklist/answer/checklistAnswerTypes';
import { ChecklistGroupBy, ChecklistGroupByDisplayMap } from '../checklist/checklistTypes';
import {
  DeviationGroupBy,
  DeviationGroupByDisplayMap,
  DeviationGroupByFieldMetaMap,
  DeviationGroupByFieldType,
} from '../deviation/deviationTypes';
import { FindingGroupBy, FindingGroupByDisplayMap } from '../finding/findingTypes';
import { PermissionType } from '../guard/guardTypes';
import {
  IncidentGroupBy,
  IncidentGroupByDisplayMap,
  IncidentGroupByFieldMetaMap,
  IncidentGroupByFieldType,
} from '../incident/incidentTypes';
import {
  InstructionGroupBy,
  InstructionGroupByDisplayMap,
  InstructionGroupByFieldMetaMap,
} from '../instruction/instructionTypes';
import { LototoItemGroupBy, LototoItemGroupByDisplayMap } from '../lototo/item/lototoItemTypes';
import { LototoGroupBy, LototoGroupByDisplayMap } from '../lototo/lototoTypes';
import {
  ObservationGroupBy,
  ObservationGroupByDisplayMap,
  ObservationGroupByFieldMetaMap,
  ObservationGroupByFieldType,
} from '../observation/observationTypes';
import {
  SafetyWalkGroupBy,
  SafetyWalkGroupByDisplayMap,
  SafetyWalkGroupByFieldMetaMap,
  SafetyWalkGroupByFieldType,
} from '../safety-walk/safetyWalkTypes';
import { ShiftLogGroupBy, ShiftLogGroupByDisplayMap } from '../shift-report/log/shiftLogTypes';
import {
  ShiftReportGroupBy,
  ShiftReportGroupByDisplayMap,
  ShiftReportGroupByFieldMetaMap,
  ShiftReportGroupByFieldType,
} from '../shift-report/shiftReportTypes';
import { TraGroupBy, TraGroupByDisplayMap } from '../tra/traTypes';
import {
  WorkPermitGroupBy,
  WorkPermitGroupByDisplayMap,
  WorkPermitGroupByFieldMetaMap,
  WorkPermitGroupByFieldSortFunctionMap,
  WorkPermitGroupByFieldType,
} from '../work-permit/workPermitTypes';

export interface StatsParams {
  ancestorGroupId?: number;
  groupId?: number;
  processInstanceId?: string;
  module?: StatsModule | 'TASKS';
  groupBy?: StatsGroupBy[];
  filter?: string;
  startDate?: number;
  endDate?: number;
  timeZone: string;
}

export interface KeyTotal {
  key: string;
  total: number;
}

export interface TimeSeriesDataPoint {
  date: string;
  total: number | KeyTotal[];
}

export interface WeekTotalCount {
  week: number;
  year: number;
  total: number | KeyTotal[];
}

export interface KeyMetadata {
  [key: string]: unknown;
}

export interface KeyWithMetadata {
  key: string | number;
  metadata: KeyMetadata;
}

export interface CountObject {
  count: number;
  _metadata?: KeyMetadata;
}

export interface StatsKeyValue {
  [key: string]: CountObject | StatsKeyValue;
}

export enum StatsModule {
  WORKPERMITS = 'WORKPERMITS',
  TRAS = 'TRAS',
  LOTOTO_PLANS = 'LOTOTO_PLANS',
  LOTOTO_ITEMS = 'LOTOTO_ITEMS',
  INCIDENTS = 'INCIDENTS',
  OBSERVATIONS = 'OBSERVATIONS',
  SAFETYWALKS = 'SAFETYWALKS',
  INSTRUCTIONS = 'INSTRUCTIONS',
  DEVIATIONS = 'DEVIATIONS',
  SHIFT_REPORTS = 'SHIFT_REPORTS',
  SHIFT_REPORT_LOGS = 'SHIFT_REPORT_LOGS',
  CHANGES = 'CHANGES',
  CHECKLISTS = 'CHECKLISTS',
  CHECKLIST_ANSWERS = 'CHECKLIST_ANSWERS',
  INTERNAL_AUDITS = 'INTERNAL_AUDITS',
  EXTERNAL_AUDITS = 'EXTERNAL_AUDITS',
  FINDINGS = 'FINDINGS',
}

export const StatsModuleDisplayMap: Record<StatsModule | 'TASKS', string> = {
  TASKS: 'Tasks',
  WORKPERMITS: 'Work permits',
  TRAS: "TRA's",
  LOTOTO_PLANS: 'Lototo plans',
  LOTOTO_ITEMS: 'Lototo items',
  INCIDENTS: 'Incidents',
  OBSERVATIONS: 'Observations',
  SAFETYWALKS: 'Safety walks',
  INSTRUCTIONS: 'Instructions',
  DEVIATIONS: 'Deviations',
  SHIFT_REPORTS: 'Shift reports',
  SHIFT_REPORT_LOGS: 'Shift logs',
  CHANGES: 'Changes',
  CHECKLISTS: 'Checklists',
  CHECKLIST_ANSWERS: 'Checklist answers',
  INTERNAL_AUDITS: 'Internal audits',
  EXTERNAL_AUDITS: 'External audits',
  FINDINGS: 'Findings',
};

export const STATS_MODULE_PERMISSION_MAP: Record<StatsModule, PermissionType> = {
  [StatsModule.WORKPERMITS]: PermissionType.WORKPERMIT_READ,
  [StatsModule.INCIDENTS]: PermissionType.INCIDENT_READ,
  [StatsModule.OBSERVATIONS]: PermissionType.OBSERVATION_READ,
  [StatsModule.SAFETYWALKS]: PermissionType.OBSERVATION_READ,
  [StatsModule.INSTRUCTIONS]: PermissionType.INSTRUCTION_READ,
  [StatsModule.DEVIATIONS]: PermissionType.DEVIATION_READ,
  [StatsModule.SHIFT_REPORTS]: PermissionType.SHIFT_REPORT_READ,
  [StatsModule.SHIFT_REPORT_LOGS]: PermissionType.SHIFT_REPORT_READ,
  [StatsModule.CHANGES]: PermissionType.CHANGE_READ,
  [StatsModule.CHECKLISTS]: PermissionType.CHANGE_READ,
  [StatsModule.CHECKLIST_ANSWERS]: PermissionType.CHANGE_READ,
  [StatsModule.INTERNAL_AUDITS]: PermissionType.FINDING_READ,
  [StatsModule.EXTERNAL_AUDITS]: PermissionType.FINDING_READ,
  [StatsModule.FINDINGS]: PermissionType.FINDING_READ,
  [StatsModule.LOTOTO_PLANS]: PermissionType.LOTOTO_READ,
  [StatsModule.LOTOTO_ITEMS]: PermissionType.LOTOTO_READ,
  [StatsModule.TRAS]: PermissionType.WORKPERMIT_READ,
};

const StatsGroupByRecord = {
  ...mapEnumValues(WorkPermitGroupBy),
  ...mapEnumValues(TraGroupBy),
  ...mapEnumValues(IncidentGroupBy),
  ...mapEnumValues(LototoGroupBy),
  ...mapEnumValues(LototoItemGroupBy),
  ...mapEnumValues(ObservationGroupBy),
  ...mapEnumValues(SafetyWalkGroupBy),
  ...mapEnumValues(InstructionGroupBy),
  ...mapEnumValues(DeviationGroupBy),
  ...mapEnumValues(ShiftReportGroupBy),
  ...mapEnumValues(ShiftLogGroupBy),
  ...mapEnumValues(ChangeGroupBy),
  ...mapEnumValues(ChecklistGroupBy),
  ...mapEnumValues(ChecklistAnswerGroupBy),
  ...mapEnumValues(InternalAuditGroupBy),
  ...mapEnumValues(ExternalAuditGroupBy),
  ...mapEnumValues(FindingGroupBy),
} as const;

export type StatsGroupBy = keyof typeof StatsGroupByRecord;

export { StatsGroupByRecord as StatsGroupBy };

export const StatsGroupByDisplayMap: Record<StatsGroupBy, string> = {
  ...WorkPermitGroupByDisplayMap,
  ...TraGroupByDisplayMap,
  ...IncidentGroupByDisplayMap,
  ...LototoGroupByDisplayMap,
  ...LototoItemGroupByDisplayMap,
  ...ObservationGroupByDisplayMap,
  ...SafetyWalkGroupByDisplayMap,
  ...InstructionGroupByDisplayMap,
  ...DeviationGroupByDisplayMap,
  ...ShiftReportGroupByDisplayMap,
  ...ShiftLogGroupByDisplayMap,
  ...ChangeGroupByDisplayMap,
  ...ChecklistGroupByDisplayMap,
  ...ChecklistAnswerGroupByDisplayMap,
  ...InternalAuditGroupByDisplayMap,
  ...ExternalAuditGroupByDisplayMap,
  ...FindingGroupByDisplayMap,
};

export type StatsGroupByToEnum =
  | WorkPermitGroupByFieldType
  | IncidentGroupByFieldType
  | ObservationGroupByFieldType
  | SafetyWalkGroupByFieldType
  | DeviationGroupByFieldType
  | ShiftReportGroupByFieldType;

type StatsGroupByFieldMetaMapType = {
  [K in keyof StatsGroupByToEnum]: EnumMetaMap<StatsGroupByToEnum[K]>;
};

export const StatsGroupByFieldMetaMap = {
  ...WorkPermitGroupByFieldMetaMap,
  ...IncidentGroupByFieldMetaMap,
  ...InstructionGroupByFieldMetaMap,
  ...DeviationGroupByFieldMetaMap,
  ...ShiftReportGroupByFieldMetaMap,
  ...ObservationGroupByFieldMetaMap,
  ...SafetyWalkGroupByFieldMetaMap,
} as const satisfies StatsGroupByFieldMetaMapType;

export function hasMetaMap(k: StatsGroupBy): k is keyof typeof StatsGroupByFieldMetaMap {
  return k in StatsGroupByFieldMetaMap;
}

export const StatsGroupByFieldSortFunctionMap = {
  ...WorkPermitGroupByFieldSortFunctionMap,
}

export const StatsTimeSeriesGroupByModuleMap: Partial<Record<StatsModule, StatsGroupBy>> = {
  INCIDENTS: StatsGroupByRecord.INCIDENTS_CREATION_DATE_WEEK,
  WORKPERMITS: StatsGroupByRecord.WORKPERMITS_CREATION_DATE_WEEK,
  TRAS: StatsGroupByRecord.TRAS_CREATION_DATE_WEEK,
  LOTOTO_PLANS: StatsGroupByRecord.LOTOTO_PLANS_PLANNED_DATE_WEEK,
  LOTOTO_ITEMS: StatsGroupByRecord.LOTOTO_ITEMS_PLANNED_DATE_WEEK,
  INSTRUCTIONS: StatsGroupByRecord.INSTRUCTIONS_CREATION_DATE_WEEK,
  DEVIATIONS: StatsGroupByRecord.DEVIATIONS_CREATION_DATE_WEEK,
  SHIFT_REPORTS: StatsGroupByRecord.SHIFT_REPORTS_CREATION_DATE_WEEK,
  SHIFT_REPORT_LOGS: StatsGroupByRecord.SHIFT_REPORT_LOGS_CREATION_DATE_WEEK,
  OBSERVATIONS: StatsGroupByRecord.OBSERVATIONS_DATE_WEEK,
  SAFETYWALKS: StatsGroupByRecord.SAFETYWALKS_DATE_WEEK,
  CHANGES: StatsGroupByRecord.CHANGES_START_DATE_WEEK,
  CHECKLISTS: StatsGroupByRecord.CHECKLISTS_START_DATE_WEEK,
  CHECKLIST_ANSWERS: StatsGroupByRecord.CHECKLIST_ANSWERS_START_DATE_WEEK,
  INTERNAL_AUDITS: StatsGroupByRecord.INTERNAL_AUDITS_DATE_WEEK,
  EXTERNAL_AUDITS: StatsGroupByRecord.EXTERNAL_AUDITS_DATE_WEEK,
  FINDINGS: StatsGroupByRecord.FINDINGS_DATE_WEEK,
};

export const StatsThemeColorToRgbMap: Record<string, string> = {
  primary: 'rgb(37, 196, 151)',
  'success.main': 'rgb(37, 196, 151)',
  'success.dark': 'rgb(7, 171, 157)',
  'error.dark': 'rgb(198, 40, 40)',
  'error.main': 'rgb(198, 40, 40)',
  'error.light': 'rgb(239, 83, 80)',
  'warning.main': 'rgb(255, 152, 0)',
  'warning.light': 'rgb(255, 152, 0)',
  'disabled.main': 'rgba(0, 0, 0, 0.26)',
  'rgb(255, 235, 59)': 'rgb(255, 235, 59)',
};
