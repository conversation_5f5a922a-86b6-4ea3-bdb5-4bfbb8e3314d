import { useMemo } from 'react';
import { BarDatumWithColor, ResponsiveBar } from '@nivo/bar';
import { StatsModuleDisplayMap, StatsModule, StatsKeyValue, StatsGroupBy, StatsGroupByFieldSortFunctionMap } from '../../stats/statsTypes';
import useAxisMargin, { AxisOrientation } from '../hooks/useAxisMargin';
import { truncate } from '../chartFunctions';
import { ChartOrientation } from '../chartTypes';
import useLongestTick from '../hooks/useLongestTick';

interface BarChartProps {
  data: StatsKeyValue;
  module: StatsModule;
  fieldGroupByColorMapping: Record<string, string>;
  fieldStackedByColorMapping: Record<string, string>;
  fieldGroupByNamingMapping: Record<string, string>;
  fieldStackedByNamingMapping: Record<string, string>;
  orientation?: ChartOrientation;
  groupBy?: StatsGroupBy;
  stackedBy?: StatsGroupBy;
}

function BarChart({
  data,
  module,
  fieldGroupByColorMapping,
  fieldStackedByColorMapping,
  fieldGroupByNamingMapping,
  fieldStackedByNamingMapping,
  orientation = ChartOrientation.VERTICAL,
  groupBy,
  stackedBy,
}: BarChartProps) {
  const isHorizontal = orientation === ChartOrientation.HORIZONTAL;
  const defaultMargin = 8;
  const topMargin = defaultMargin;
  const rightMargin = isHorizontal ? defaultMargin + 5 : defaultMargin;
  const truncateLabelsAt = 25;

  const displayGroupByColorMapping = useMemo(
    () =>
      Object.entries(fieldGroupByColorMapping).reduce<Record<string, string>>((acc, [raw, color]) => {
        const display = fieldGroupByNamingMapping[raw] || raw;
        acc[display] = color;
        return acc;
      }, {}),
    [fieldGroupByColorMapping, fieldGroupByNamingMapping]
  );

  const displayStackedByColorMapping = useMemo(
    () =>
      Object.entries(fieldStackedByColorMapping).reduce<Record<string, string>>((acc, [raw, color]) => {
        const display = fieldStackedByNamingMapping[raw] || raw;
        acc[display] = color;
        return acc;
      }, {}),
    [fieldStackedByColorMapping, fieldStackedByNamingMapping]
  );

  const stackedKeys = useMemo(() => {
    if (!stackedBy) return [];
    const keys = new Set<string>();
    Object.values(data).forEach((primaryValue) => {
      if (typeof primaryValue === 'object' && !('count' in primaryValue)) {
        Object.keys(primaryValue).forEach((k) => keys.add(k));
      }
    });
    return Array.from(keys);
  }, [data, stackedBy]);

  const displayStackKeys = useMemo(
    () => stackedKeys.map((raw) => fieldStackedByNamingMapping[raw] || raw),
    [stackedKeys, fieldStackedByNamingMapping]
  );

  const barData = useMemo(() => {
    // Get sort function for the current groupBy if available
    const sortFunction = groupBy && groupBy in StatsGroupByFieldSortFunctionMap
      ? StatsGroupByFieldSortFunctionMap[groupBy as keyof typeof StatsGroupByFieldSortFunctionMap]
      : undefined;

    if (!stackedBy) {
      let entries = Object.entries(data);

      // Apply sorting before display mapping if sort function exists
      if (sortFunction) {
        entries = entries.sort(([a], [b]) => sortFunction(a, b));
      }

      return entries.map(([rawKey, value]) => ({
        Group: fieldGroupByNamingMapping[rawKey] || rawKey,
        [StatsModuleDisplayMap[module]]: 'count' in value ? value.count : 0,
      }));
    }

    let entries = Object.entries(data)
      .filter(([, primaryValue]) => typeof primaryValue === 'object' && !('count' in primaryValue));

    // Apply sorting before display mapping if sort function exists
    if (sortFunction) {
      entries = entries.sort(([a], [b]) => sortFunction(a, b));
    }

    return entries.map(([rawPrimary, primaryValue]) => {
      const displayPrimary = fieldGroupByNamingMapping[rawPrimary] || rawPrimary;
      const datum: Record<string, number | string> = {
        Group: displayPrimary,
      };
      Object.entries(primaryValue as Record<string, { count: number }>).forEach(([rawStack, stackVal]) => {
        const displayStack = fieldStackedByNamingMapping[rawStack] || rawStack;
        datum[displayStack] = stackVal.count || 0;
      });
      return datum;
    });
  }, [stackedBy, data, fieldGroupByNamingMapping, module, fieldStackedByNamingMapping, groupBy]);

  const longestKey = useMemo(() => {
    const keys = Object.values(fieldGroupByNamingMapping || {});
    return keys.reduce(
      (a, b) => (truncate(a, truncateLabelsAt).length > truncate(b, truncateLabelsAt).length ? a : b),
      ''
    );
  }, [fieldGroupByNamingMapping]);

  const nonNullData = data || {};
  const longestValue = useLongestTick(
    !stackedBy
      ? Math.max(
          ...Object.values(nonNullData)
            .filter(
              (value): value is { count: number } => typeof value === 'object' && value !== null && 'count' in value
            )
            .map((value) => value.count)
        )
      : Math.max(
          ...Object.values(nonNullData)
            .filter(
              (primaryValue): primaryValue is Record<string, { count: number }> =>
                typeof primaryValue === 'object' && !('count' in primaryValue)
            )
            .map((primaryValue) => Object.values(primaryValue).reduce((sum, { count }) => sum + (count || 0), 0))
        )
  );

  const {
    margin: bottomMargin,
    axisRef: bottomAxisRef,
    adjustedAxisOptions: bottomAxisOptions,
  } = useAxisMargin({
    axisOptions: {
      tickRotation: isHorizontal ? 0 : -45,
      truncateTickAt: isHorizontal ? 0 : truncateLabelsAt,
    },
    orientation: AxisOrientation.HORIZONTAL,
    axisDefaultMargin: defaultMargin,
  });
  const {
    margin: leftMargin,
    axisRef: leftAxisRef,
    adjustedAxisOptions: leftAxisOptions,
  } = useAxisMargin({
    axisOptions: {
      truncateTickAt: isHorizontal ? truncateLabelsAt : 0,
    },
    orientation: AxisOrientation.VERTICAL,
    axisDefaultMargin: defaultMargin,
  });

  return (
    <>
      <span
        ref={leftAxisRef}
        style={{
          position: 'absolute',
          visibility: 'hidden',
          pointerEvents: 'none',
          whiteSpace: 'pre',
          fontFamily: 'sans-serif',
          fontSize: '11px',
          fontWeight: 400,
        }}
      >
        {isHorizontal ? truncate(longestKey, truncateLabelsAt) : longestValue}
      </span>
      <span
        ref={bottomAxisRef}
        style={{
          position: 'absolute',
          visibility: 'hidden',
          pointerEvents: 'none',
          whiteSpace: 'pre',
          fontFamily: 'sans-serif',
          fontSize: '11px',
          fontWeight: 400,
        }}
      >
        {isHorizontal ? longestValue : truncate(longestKey, truncateLabelsAt)}
      </span>

      <ResponsiveBar
        data={barData as BarDatumWithColor[]}
        keys={stackedBy ? displayStackKeys : [StatsModuleDisplayMap[module]]}
        indexBy="Group"
        margin={{ top: topMargin, right: rightMargin, bottom: bottomMargin, left: leftMargin }}
        padding={0.3}
        layout={isHorizontal ? 'horizontal' : 'vertical'}
        axisBottom={bottomAxisOptions}
        axisLeft={leftAxisOptions}
        colors={
          stackedBy
            ? ({ id }) => displayStackedByColorMapping[id]
            : ({ data: colorData }) => displayGroupByColorMapping[colorData.Group]
        }
        enableLabel={false}
        animate
        motionConfig="gentle"
      />
    </>
  );
}

export default BarChart;
